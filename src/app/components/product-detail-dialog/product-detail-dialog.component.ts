import {
  Component,
  ViewEncapsulation,
  HostBinding,
  Inject,
  OnInit,
  OnDestroy,
} from '@angular/core';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { NgForOf, NgIf } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { ApiService } from '../../core/services/api.service';

export interface ProductDetailData {
  productId: string;
  productImage?: string;
}

export interface ProductDetail {
  _id: string;
  name: string;
  price: number;
  priceOld?: number;
  description: string;
  thumbail: string;
  pictures: string[];
  classify: any[];
  trademark: string;
  transport: string;
  revenue: number;
  watched: number;
  storeName: string;
  categoryName: string;
}

@Component({
  selector: 'app-product-detail-dialog',
  standalone: true,
  encapsulation: ViewEncapsulation.None,
  imports: [NgForOf, NgIf, FormsModule],
  templateUrl: './product-detail-dialog.component.html',
  styleUrls: ['./product-detail-dialog.component.scss'],
})
export class ProductDetailDialog implements OnInit, OnDestroy {
  @HostBinding('style.display') display = 'contents';

  private destroy$ = new Subject<void>();

  // Product data
  product: ProductDetail | null = null;
  loading = true;
  error = false;

  // UI state
  selectedOption = '';
  expandedContent = false;
  quantity = 1;
  selectedImageIndex = 0;

  // Default values
  ratingImage = 'assets/group-2217.svg';

  constructor(
    private dialogRef: MatDialogRef<ProductDetailDialog>,
    @Inject(MAT_DIALOG_DATA) public data: ProductDetailData,
    private apiService: ApiService
  ) {}

  ngOnInit() {
    this.loadProductDetail();
  }

  ngOnDestroy() {
    this.destroy$.next();
    this.destroy$.complete();
  }

  /**
   * Load product detail from API
   */
  loadProductDetail() {
    this.loading = true;
    this.error = false;

    this.apiService
      .get(`user/api/chi-tiet-san-pham/${this.data.productId}.html`, {
        output: 'json',
      })
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (response: any) => {
          this.loading = false;

          if (response && !response.error && response.data?.product) {
            this.product = response.data.product;

            // Set default selected option if classify exists
            if (this.product.classify && this.product.classify.length > 0) {
              this.selectedOption =
                this.product.classify[0].data?.[0]?.name || '';
            }
          } else {
            this.error = true;
          }
        },
        error: (error) => {
          this.loading = false;
          this.error = true;
          console.error('Error loading product detail:', error);
        },
      });
  }

  /**
   * Get product image URL
   */
  getProductImageUrl(image: string): string {
    if (!image) return 'assets/images/placeholder.svg';
    if (image.startsWith('http')) return image;
    return `http://************:4000/${image}`;
  }

  /**
   * Get current product image
   */
  getCurrentImage(): string {
    if (!this.product)
      return this.data.productImage || 'assets/images/placeholder.svg';

    if (this.product.pictures && this.product.pictures.length > 0) {
      return this.getProductImageUrl(
        this.product.pictures[this.selectedImageIndex]
      );
    }

    return this.getProductImageUrl(this.product.thumbail);
  }

  /**
   * Select option
   */
  selectOption(option: any) {
    this.selectedOption = option.name;
  }

  /**
   * Toggle description
   */
  viewMore() {
    this.expandedContent = !this.expandedContent;
  }

  /**
   * Update quantity
   */
  updateQuantity(change: number) {
    const newQuantity = this.quantity + change;
    if (newQuantity >= 1) {
      this.quantity = newQuantity;
    }
  }

  /**
   * Select image
   */
  selectImage(index: number) {
    this.selectedImageIndex = index;
  }

  /**
   * Format price
   */
  formatPrice(price: number): string {
    return new Intl.NumberFormat('vi-VN', {
      style: 'currency',
      currency: 'VND',
    }).format(price);
  }

  /**
   * Calculate discount percentage
   */
  getDiscountPercentage(): string {
    if (
      !this.product?.priceOld ||
      this.product.priceOld <= this.product.price
    ) {
      return '';
    }
    const discount = Math.round(
      ((this.product.priceOld - this.product.price) / this.product.priceOld) *
        100
    );
    return `-${discount}%`;
  }

  /**
   * Add to cart
   */
  onAddToCart() {
    const cartData = {
      product: this.product,
      quantity: this.quantity,
      selectedOption: this.selectedOption,
    };

    this.dialogRef.close({ action: 'addToCart', data: cartData });
  }

  /**
   * Buy now
   */
  onBuyNow() {
    const orderData = {
      product: this.product,
      quantity: this.quantity,
      selectedOption: this.selectedOption,
    };

    this.dialogRef.close({ action: 'buyNow', data: orderData });
  }

  /**
   * Close dialog
   */
  onClose() {
    this.dialogRef.close();
  }

  /**
   * Handle image error
   */
  handleImageError(event: Event): void {
    const imgElement = event.target as HTMLImageElement;
    imgElement.src = 'assets/images/placeholder.svg';
    imgElement.classList.add('img-error');
  }
}
