// Import original FrameComponent8 styles
@import '../FrameComponent8/FrameComponent8.component.scss';

// Dialog specific styles
.product-detail-dialog {
  position: relative;
  max-width: 1200px;
  max-height: 90vh;
  overflow-y: auto;
  background: white;
  border-radius: 12px;
  
  // Close button
  .close-button {
    position: absolute;
    top: 16px;
    right: 16px;
    z-index: 1000;
    background: rgba(0, 0, 0, 0.5);
    border: none;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    cursor: pointer;
    transition: all 0.3s ease;
    
    &:hover {
      background: rgba(0, 0, 0, 0.7);
      transform: scale(1.1);
    }
    
    i {
      font-size: 18px;
    }
  }
}

// Image group with hover effects
.image-group {
  position: relative;
  
  .product-image-container {
    position: relative;
    overflow: hidden;
    border-radius: 12px;
    
    .image-overlay {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: rgba(0, 0, 0, 0);
      transition: all 0.3s ease;
      z-index: 1;
      pointer-events: none;
    }
    
    &:hover .image-overlay {
      background: rgba(0, 0, 0, 0.2);
    }
    
    .product-main-image {
      width: 100%;
      height: 400px;
      object-fit: cover;
      transition: transform 0.3s ease;
    }
    
    &:hover .product-main-image {
      transform: scale(1.05);
    }
    
    .rating-overlay {
      bottom: 16px;
      right: 16px;
      z-index: 2;
    }
  }
  
  // Thumbnail images
  .thumbnail-container {
    display: flex;
    gap: 8px;
    margin-top: 12px;
    flex-wrap: wrap;
    
    .thumbnail-image {
      width: 60px;
      height: 60px;
      object-fit: cover;
      border-radius: 8px;
      cursor: pointer;
      border: 2px solid transparent;
      transition: all 0.3s ease;
      
      &:hover {
        border-color: var(--color-steelblue-300);
        transform: scale(1.05);
      }
      
      &.active {
        border-color: var(--color-steelblue-300);
        box-shadow: 0 0 8px rgba(0, 123, 255, 0.3);
      }
    }
  }
}

// Loading and error states
.loading-container,
.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 300px;
  padding: 40px;
  text-align: center;
  
  .spinner-border {
    width: 3rem;
    height: 3rem;
    border-width: 0.3em;
    margin-bottom: 16px;
  }
  
  i {
    font-size: 48px;
    color: #dc3545;
    margin-bottom: 16px;
  }
  
  h4 {
    margin-bottom: 16px;
    color: #6c757d;
  }
  
  .retry-button {
    background: var(--color-steelblue-300);
    color: white;
    border: none;
    padding: 12px 24px;
    border-radius: 8px;
    cursor: pointer;
    transition: background 0.3s ease;
    
    &:hover {
      background: var(--color-steelblue-200);
    }
  }
}

// Product options styling
.classify-group {
  margin-bottom: 20px;
  
  .classify-title {
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 12px;
    color: var(--color-steelblue-300);
  }
  
  .options-container {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
  }
  
  .group-button {
    position: relative;
    border: 2px solid #e0e0e0;
    background: white;
    border-radius: 8px;
    padding: 8px 16px;
    cursor: pointer;
    transition: all 0.3s ease;
    
    &:hover {
      border-color: var(--color-steelblue-300);
    }
    
    &.active {
      border-color: var(--color-steelblue-300);
      background: rgba(0, 123, 255, 0.1);
      
      .op2 {
        color: var(--color-steelblue-300);
        font-weight: 600;
      }
    }
    
    .option-price {
      font-size: 12px;
      color: #6c757d;
      margin-top: 4px;
    }
  }
}

// Quantity controls
.quantity-controls {
  display: flex;
  align-items: center;
  gap: 8px;
  
  .control-container {
    width: 36px;
    height: 36px;
    border: 1px solid #e0e0e0;
    background: white;
    border-radius: 6px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    
    &:hover {
      border-color: var(--color-steelblue-300);
      background: rgba(0, 123, 255, 0.05);
    }
    
    .quantity-buttons {
      font-size: 18px;
      font-weight: bold;
      color: var(--color-steelblue-300);
    }
  }
  
  .quantity-input {
    width: 60px;
    height: 36px;
    text-align: center;
    border: 1px solid #e0e0e0;
    border-radius: 6px;
    font-size: 16px;
    
    &:focus {
      outline: none;
      border-color: var(--color-steelblue-300);
      box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.1);
    }
  }
}

// Action buttons
.cart-actions-container {
  display: flex;
  gap: 12px;
  width: 100%;
  
  .cart-actions,
  .rectangle-parent15 {
    flex: 1;
    padding: 16px;
    border-radius: 8px;
    border: none;
    cursor: pointer;
    font-weight: 600;
    transition: all 0.3s ease;
    
    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    }
  }
  
  .cart-actions {
    background: white;
    border: 2px solid var(--color-steelblue-300);
    color: var(--color-steelblue-300);
    
    &:hover {
      background: var(--color-steelblue-300);
      color: white;
    }
  }
  
  .rectangle-parent15 {
    background: var(--color-steelblue-300);
    color: white;
    
    &:hover {
      background: var(--color-steelblue-200);
    }
  }
}

// Responsive design
@media (max-width: 768px) {
  .product-detail-dialog {
    max-width: 95vw;
    margin: 10px;
    
    .frame-parent15 {
      flex-direction: column;
    }
    
    .cart-actions-container {
      flex-direction: column;
    }
    
    .thumbnail-container {
      justify-content: center;
    }
  }
}
