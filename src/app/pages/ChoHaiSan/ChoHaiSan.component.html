<div class="container">
  <main class="cho-hai-san-inner">
    <div class="frame-parent">
      <div class="frame-group align-items-center">
        <div class="frame-wrapper">
          <div class="rectangle-parent">
            <div class="frame-child"></div>
            <div class="frame-item"></div>
            <div class="frame-inner"></div>
          </div>
        </div>
        <section class="product-images">
          <img class="image-icon" alt="" src="assets/<EMAIL>" (click)="onImageClick()" />
        </section>
        <div class="frame-container">
          <div class="frame-div w-100">
            <div class="ch-hi-sn-lng-nui-bin-v-wrapper">
              <h3 class="ch-hi-sn">CHỢ HẢI SẢN - LÀNG NUÔI BIỂN VÂN ĐỒN</h3>
            </div>
            <div class="frame-parent1 justify-content-center">
              <button *ngFor="let category of categories" class="rectangle-group col-12 col-sm-4 col-md-4 col-3 align-items-center justify-content-center">
                <div class="rectangle-div"></div>
                <div class="thc-phm-ti">{{category}}</div>
              </button>
            </div>
          </div>
        </div>
        <div class="frame-parent2">
          <frame-component2  *ngFor="let product of products" [image]="product.image" [price]="product.price" [name]="product.name" [priceDiscount]="product.priceDiscount" [discount]="product.discount" (onBuyClick)="onOrderClick()"> </frame-component2>
        </div>
      </div>
      <button class="frame-wrapper1">
        <div class="rectangle-parent2">
          <div class="frame-child3"></div>
          <div class="thc-phm-ti">XEM THÊM SẢN PHẨM</div>
        </div>
      </button>
    </div>
  </main>

</div>
