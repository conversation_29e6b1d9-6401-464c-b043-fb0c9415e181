
// Lower z-index for Material UI form fields to avoid conflicts
.mat-mdc-form-field {
  z-index: inherit !important;
}

// Ensure Material dialog doesn't interfere
.mat-mdc-dialog-container {
  z-index: 1000 !important;
}

// Lower z-index for overlays that might conflict
.cdk-overlay-pane {
  z-index: auto !important;
}

:root {
  /* Color */
  --color-whitesmoke: #f3f3f3;
  --color-darkslategray-200: #333;
  --color-darkslategray-100: #3f3f3f;
  --color-darkgray-200: #979797;
  --color-darkgray-100: #9d9d9d;
  --color-darkslateblue: #073a69;
  --color-gainsboro-200: #d9d9d9;
  --color-gainsboro-100: #dadada;
  --color-black: #000;
  --color-crimson-200: #d42023;
  --color-crimson-100: #ea3941;
  --color-gray-500: rgba(255, 255, 255, 0.5);
  --color-gray-400: rgba(0, 0, 0, 0.7);
  --color-gray-300: rgba(0, 0, 0, 0.5);
  --color-gray-200: #01061f;
  --color-gray-100: #222;
  --color-steelblue-300: #005b94;
  --color-steelblue-200: #2682ba;
  --color-steelblue-100: #5a94c1;
  --color-white: #fff;

  /* Gap */
  --gap-6: 6px;
  --gap-11: 11px;
  --gap-15: 15px;
  --gap-64: 64px;
  --gap-32: 32px;
  --gap-60: 60px;
  --gap-16: 16px;
  --gap-2: 2px;
  --gap-4: 4px;
  --gap-30: 30px;
  --gap-18: 18px;
  --gap-24: 24px;
  --gap-48: 48px;
  --gap-22: 22px;
  --gap-8: 8px;
  --gap-10: 10px;
  --gap-20: 20px;
  --gap-14: 14px;

  /* Padding */
  --padding-36: 36px;
  --padding-10: 10px;
  --padding-28: 28px;
  --padding-100: 100px;
  --padding-24: 24px;
  --padding-1: 1px;
  --padding-4: 4px;
  --padding-60: 60px;
  --padding-3: 3px;
  --padding-8: 8px;
  --padding-30: 30px;
  --padding-15: 15px;
  --padding-40: 40px;
  --padding-80: 80px;
  --padding-2: 2px;
  --padding-12: 12px;
  --padding-14: 14px;
  --padding-6: 6px;
  --padding-5: 5px;
  --padding-22: 22px;
  --padding-11: 11px;
  --padding-20: 20px;
  --padding-19: 19px;
  --padding-16: 16px;

  /* BorderRadius */
  --br-4: 4px;
  --br-3: 3px;
  --br-10: 10px;
  --br-100: 100px;
  --br-5: 5px;

  /* Font */
  --font-body: Roboto, "Helvetica Neue", sans-serif;

  /* FontSize */
  --font-size-20: 20px;
  --font-size-12: 12px;
  --font-size-14: 14px;
  --font-size-16: 16px;
  --font-size-8: 8px;
  --font-size-19: 19px;
  --font-size-24: 24px;
  --font-size-36: 36px;

}


html, body {
  height: 100%;
}

body {
  margin: 0;
  font-family: var(--font-body);
}

input::placeholder {
  opacity: 1 !important;
  color: #9D9D9D !important;
}

textarea::placeholder {
  opacity: 1 !important;
  color: #9D9D9D !important;
}

// Custom styles for ngx-daterangepicker-material
.daterangepicker {
  font-family: var(--font-body);
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);

  .calendar-table {
    .available:hover {
      background-color: #e3f2fd;
      color: #1976d2;
    }

    .active {
      background-color: #1976d2 !important;
      color: white !important;
    }

    .in-range {
      background-color: #e3f2fd;
      color: #1976d2;
    }
  }

  .ranges {
    ul {
      li {
        &:hover {
          background-color: #f5f5f5;
        }

        &.active {
          background-color: #1976d2;
          color: white;
        }
      }
    }
  }

  .drp-buttons {
    .btn {
      &.btn-primary {
        background-color: #1976d2;
        border-color: #1976d2;

        &:hover {
          background-color: #1565c0;
          border-color: #1565c0;
        }
      }

      &.btn-default {
        background-color: #f5f5f5;
        border-color: #e0e0e0;
        color: #666;

        &:hover {
          background-color: #eeeeee;
        }
      }
    }
  }
}
