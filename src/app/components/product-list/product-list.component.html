<div class="product-list-container">
  <!-- Header v<PERSON><PERSON> tìm kiếm và filter -->
  <div class="search-filter-section">
    <div class="search-bar">
      <input 
        type="text" 
        [(ngModel)]="searchText" 
        placeholder="Tì<PERSON> kiếm sản phẩm..." 
        class="search-input"
        (keyup.enter)="onSearch()">
      <button class="search-btn" (click)="onSearch()">
        <i class="fas fa-search"></i>
      </button>
    </div>

    <!-- Categories Filter -->
    <div class="categories-section">
      <h3>Danh mục</h3>
      <div class="categories-list">
        <button 
          class="category-btn" 
          [class.active]="selectedCategoryId === ''"
          (click)="onCategorySelect('')">
          Tất cả
        </button>
        <button 
          *ngFor="let category of categories; trackBy: trackByCategoryId"
          class="category-btn"
          [class.active]="selectedCategoryId === category._id"
          (click)="onCategorySelect(category._id)">
          {{ category.name }}
        </button>
      </div>
    </div>

    <!-- Sort and Filter Options -->
    <div class="filter-options">
      <div class="filter-group">
        <label>Sắp xếp:</label>
        <select [(ngModel)]="currentOrder" (change)="onSortChange(currentOrder)">
          <option value="sales">Bán chạy</option>
          <option value="price-asc">Giá thấp đến cao</option>
          <option value="price-desc">Giá cao đến thấp</option>
          <option value="newest">Mới nhất</option>
        </select>
      </div>

      <div class="filter-group">
        <label>Vận chuyển:</label>
        <select [(ngModel)]="currentShipping" (change)="onShippingChange(currentShipping)">
          <option value="">Tất cả</option>
          <option value="Giao hàng tiết kiệm">Giao hàng tiết kiệm</option>
          <option value="Giao hàng nhanh">Giao hàng nhanh</option>
        </select>
      </div>

      <div class="filter-group">
        <label>Khoảng giá:</label>
        <select [(ngModel)]="currentPrice" (change)="onPriceChange(currentPrice)">
          <option value="">Tất cả</option>
          <option value="0-100000">Dưới 100,000đ</option>
          <option value="100000-500000">100,000đ - 500,000đ</option>
          <option value="500000-1000000">500,000đ - 1,000,000đ</option>
          <option value="1000000-">Trên 1,000,000đ</option>
        </select>
      </div>

      <button class="clear-filters-btn" (click)="onClearFilters()">
        Xóa bộ lọc
      </button>
    </div>
  </div>

  <!-- Loading indicator -->
  <div *ngIf="loading && products.length === 0" class="loading-container">
    <div class="spinner"></div>
    <p>Đang tải sản phẩm...</p>
  </div>

  <!-- Products Grid -->
  <div class="products-grid" *ngIf="!loading || products.length > 0">
    <div 
      *ngFor="let product of products; trackBy: trackByProductId"
      class="product-card">
      
      <div class="product-image">
        <img 
          [src]="getProductImageUrl(product.thumbail)" 
          [alt]="product.name"
          (error)="$event.target.src='assets/images/no-image.png'">
        
        <!-- Sale badge -->
        <div *ngIf="product.priceOld && product.priceOld > product.price" class="sale-badge">
          Sale
        </div>
        
        <!-- Status badge -->
        <div class="status-badge" [class.in-stock]="product.typeProduct === 1" [class.out-of-stock]="product.typeProduct === 0">
          {{ product.typeProduct === 1 ? 'Còn hàng' : 'Hết hàng' }}
        </div>
      </div>

      <div class="product-info">
        <h4 class="product-name">{{ product.name }}</h4>
        <p class="product-store">{{ product.storeName }}</p>
        
        <div class="product-pricing">
          <span class="current-price">{{ formatPrice(product.price) }}</span>
          <span *ngIf="product.priceOld && product.priceOld > product.price" 
                class="old-price">{{ formatPrice(product.priceOld) }}</span>
        </div>

        <div class="product-meta">
          <span class="shipping">{{ product.transport }}</span>
          <span class="revenue">Đã bán: {{ product.revenue }}</span>
        </div>

        <div class="product-actions">
          <button class="view-detail-btn">Xem chi tiết</button>
          <button class="add-to-cart-btn" [disabled]="product.typeProduct === 0">
            Thêm vào giỏ
          </button>
        </div>
      </div>
    </div>
  </div>

  <!-- Empty state -->
  <div *ngIf="!loading && products.length === 0" class="empty-state">
    <i class="fas fa-box-open"></i>
    <h3>Không tìm thấy sản phẩm</h3>
    <p>Thử thay đổi từ khóa tìm kiếm hoặc bộ lọc</p>
  </div>

  <!-- Load More Button -->
  <div *ngIf="hasMore && products.length > 0" class="load-more-section">
    <button 
      class="load-more-btn" 
      (click)="onLoadMore()" 
      [disabled]="loading">
      <span *ngIf="!loading">Xem thêm</span>
      <span *ngIf="loading">
        <div class="small-spinner"></div>
        Đang tải...
      </span>
    </button>
  </div>

  <!-- End message -->
  <div *ngIf="!hasMore && products.length > 0" class="end-message">
    <p>Đã hiển thị tất cả sản phẩm</p>
  </div>
</div>
