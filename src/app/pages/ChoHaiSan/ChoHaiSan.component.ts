import {Component, ViewEncapsulation, HostBinding, OnInit} from '@angular/core';

import { FrameComponent2 } from '../../components/FrameComponent2/FrameComponent2.component';
import { FrameComponent3 } from '../../components/FrameComponent3/FrameComponent3.component';

import { NavbarComponent } from '../../components/navbar/navbar.component';
import {CopyrightComponent} from "../../components/footer/copyright.component";
import {NgForOf} from "@angular/common";
import {BookingDialogComponent} from "../../shared/components/booking-dialog/booking-dialog.component";
import {MatDialog} from "@angular/material/dialog";
import {ChonMon} from "../ChonMon/ChonMon.component";

@Component({
  selector: 'cho-hai-san',
  standalone: true,
  encapsulation: ViewEncapsulation.None,
  imports: [FrameComponent2, NgForOf],
  templateUrl: './ChoHaiSan.component.html',
  styleUrls: ['./ChoHaiSan.component.scss'],
})
export class ChoHaiSan implements OnInit{
  @HostBinding('style.display') display = 'contents';
  categories = ['THỰC PHẨM TƯƠI MỖI NGÀY', 'THỰC PHẨM SƠ CHẾ', 'THỰC PHẨM CHẾ BIẾN SẴN'];
// In ChoHaiSan.component.ts
  products = [
    {
      name: 'Tôm hùm Alaska',
      price: '1.500.000 ₫',
      priceDiscount: '105.000 ₫',
      discount: '80',
      image: 'assets/<EMAIL>'
    },
    {
      name: 'Cua hoàng đế',
      price: '2.500.000 ₫',
      priceDiscount: '105.000 ₫',
      discount: '80',
      image: 'assets/<EMAIL>'
    },
    {
      name: 'Tôm hùm Alaska',
      price: '1.500.000 ₫',
      priceDiscount: '105.000 ₫',
      discount: '80',
      image: 'assets/<EMAIL>'
    },
    {
      name: 'Tôm hùm Alaska',
      price: '1.500.000 ₫',
      priceDiscount: '105.000 ₫',
      discount: '80',
      image: 'assets/<EMAIL>'
    },
    {
      name: 'Cua hoàng đế',
      price: '2.500.000 ₫',
      priceDiscount: '105.000 ₫',
      discount: '80',
      image: 'assets/<EMAIL>'
    },
    {
      name: 'Tôm hùm Alaska',
      price: '1.500.000 ₫',
      priceDiscount: '105.000 ₫',
      discount: '80',
      image: 'assets/<EMAIL>'
    },{
      name: 'Tôm hùm Alaska',
      price: '1.500.000 ₫',
      priceDiscount: '105.000 ₫',
      discount: '80',
      image: 'assets/<EMAIL>'
    },
    {
      name: 'Cua hoàng đế',
      price: '2.500.000 ₫',
      priceDiscount: '105.000 ₫',
      discount: '80',
      image: 'assets/<EMAIL>'
    },
    {
      name: 'Tôm hùm Alaska',
      price: '1.500.000 ₫',
      priceDiscount: '105.000 ₫',
      discount: '80',
      image: 'assets/<EMAIL>'
    },
    // Add more products as needed
  ];
  constructor(private dialog: MatDialog) {}

  ngOnInit() {
  }
  onImageClick() {
    // Please sync "Tin tuc chi tiet" to the project
  }
  onOrderClick() {
    this.dialog.open(ChonMon, {
      disableClose: false,
      autoFocus: true,
      height: '705px'
    });
  }
}
