// Interface cho phân lo<PERSON>i sản phẩm
export interface ProductClassification {
  _id: string;
  name: string;
  data: ProductClassificationData[];
}

export interface ProductClassificationData {
  _id: string;
  mass: string;
  priceOld: string;
  price: string;
  name: string;
}

// Interface cho sản phẩm
export interface Product {
  _id: string;
  userId: string;
  name: string;
  storeId: string;
  categoryId: string;
  description: string;
  trademark: string;
  price: number;
  transport: string;
  thumbail: string;
  code: number;
  classify: ProductClassification[];
  typeProduct: number;
  revenue: number;
  watched: number;
  status: number;
  size: any[];
  pictures: any[];
  priceOld: number | null;
  typeShip: number;
  storeName: string;
  storeStatus: number;
}

// Interface cho danh mục
export interface Category {
  _id: string;
  name: string;
  status: number;
  type?: number;
  picture?: string;
  description?: string;
}

// Interface cho response của API search-v2
export interface SearchProductResponse {
  error: boolean;
  message: string;
  data: {
    result: Product[];
    totalPage: number;
    total?: number;
  };
}

// Interface cho tham số tìm kiếm
export interface SearchParams {
  search?: string;
  type?: number;
  page?: number;
  categoryId?: string;
  order?: string;
  shipping?: string;
  price?: string;
  rate?: number;
  limit?: number;
  typeShip?: number;
}

// Interface cho response chung của API
export interface ApiResponse<T> {
  error: boolean;
  message: string;
  data: T;
}
