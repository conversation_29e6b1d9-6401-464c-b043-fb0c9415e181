import { Component, ViewEncapsulation, HostBinding, OnInit, OnDestroy } from '@angular/core';

import { ProductItem } from '../../components/FrameComponent2/FrameComponent2.component';
import { FrameComponent3 } from '../../components/FrameComponent3/FrameComponent3.component';

import { NavbarComponent } from '../../components/navbar/navbar.component';
import { CopyrightComponent } from '../../components/footer/copyright.component';
import { NgForOf, NgIf } from '@angular/common';
import { BookingDialogComponent } from '../../shared/components/booking-dialog/booking-dialog.component';
import { MatDialog } from '@angular/material/dialog';
import { ChonMon } from '../ChonMon/ChonMon.component';
import { ApiService } from '../../core/services/api.service';
import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { ProductDetailDialog } from '../../components/product-detail-dialog/product-detail-dialog.component';

@Component({
  selector: 'cho-hai-san',
  standalone: true,
  encapsulation: ViewEncapsulation.None,
  imports: [ProductItem, NgForOf, NgIf],
  templateUrl: './ChoHaiSan.component.html',
  styleUrls: ['./ChoHaiSan.component.scss'],
})
export class ChoHaiSan implements OnInit, OnDestroy {
  @HostBinding('style.display') display = 'contents';

  private destroy$ = new Subject<void>();

  // API data
  categories: any[] = [];
  products: any[] = [];
  loading = false;
  hasMore = true;

  // Pagination
  currentPage = 1;
  limit = 10;
  selectedCategoryId = '';

  // Mock categories for fallback
  mockCategories = ['THỰC PHẨM TƯƠI MỖI NGÀY', 'THỰC PHẨM SƠ CHẾ', 'THỰC PHẨM CHẾ BIẾN SẴN'];
  constructor(private dialog: MatDialog, private apiService: ApiService) {}

  ngOnInit() {
    this.loadCategories();
    this.loadProducts();
  }

  ngOnDestroy() {
    this.destroy$.next();
    this.destroy$.complete();
  }

  /**
   * Load categories from API - sử dụng API categories-by-type giống như menu component
   */
  loadCategories() {
    // Sử dụng API categories-by-type với cat=0,1,2 cho sản phẩm
    this.apiService
      .get('user/api/categories-by-type', { cat: '0,1,2' })
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (response: any) => {
          console.log('Categories response:', response);

          if (response && !response.error && response.data?.categories) {
            this.categories = response.data.categories;
          } else {
            // Fallback to mock categories
            this.categories = this.mockCategories.map((name, index) => ({
              _id: `mock_${index}`,
              name: name,
            }));
          }
        },
        error: error => {
          console.error('Error loading categories:', error);
          // Fallback to mock categories
          this.categories = this.mockCategories.map((name, index) => ({
            _id: `mock_${index}`,
            name: name,
          }));
        },
      });
  }

  /**
   * Load products from API
   */
  loadProducts() {
    this.loading = true;

    const params: any = {
      search: '',
      type: 0, // 0 = sản phẩm
      page: this.currentPage,
      limit: this.limit,
    };

    if (this.selectedCategoryId) {
      params.categoryId = this.selectedCategoryId;
    }

    this.apiService
      .get('/user/api/search-v2', params)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (response: any) => {
          this.loading = false;

          if (response && !response.error && response.data?.result) {
            const newProducts = response.data.result.map((product: any) => ({
              _id: product._id,
              name: product.name,
              price: this.formatPrice(product.price),
              priceDiscount: product.priceOld ? this.formatPrice(product.priceOld) : null,
              discount: product.priceOld
                ? this.calculateDiscount(product.price, product.priceOld)
                : null,
              image: this.getProductImageUrl(product.thumbail),
              originalData: product,
            }));

            if (this.currentPage === 1) {
              this.products = newProducts;
            } else {
              this.products = [...this.products, ...newProducts];
            }

            this.hasMore = this.currentPage < (response.data.totalPage || 1);
          }
        },
        error: error => {
          this.loading = false;
          console.error('Error loading products:', error);
        },
      });
  }

  /**
   * Load more products
   */
  onLoadMore() {
    if (!this.loading && this.hasMore) {
      this.currentPage++;
      this.loadProducts();
    }
  }

  /**
   * Filter products by category
   */
  onCategorySelect(categoryId: string) {
    this.selectedCategoryId = categoryId;
    this.currentPage = 1;
    this.products = [];
    this.loadProducts();
  }

  /**
   * Format price to Vietnamese currency
   */
  formatPrice(price: number): string {
    return new Intl.NumberFormat('vi-VN', {
      style: 'currency',
      currency: 'VND',
    }).format(price);
  }

  /**
   * Calculate discount percentage
   */
  calculateDiscount(currentPrice: number, oldPrice: number): string {
    if (!oldPrice || oldPrice <= currentPrice) return '0';
    const discount = Math.round(((oldPrice - currentPrice) / oldPrice) * 100);
    return discount.toString();
  }

  /**
   * Get product image URL
   */
  getProductImageUrl(thumbail: string): string {
    if (!thumbail) return 'assets/<EMAIL>';
    if (thumbail.startsWith('http')) return thumbail;
    return `http://103.7.43.223:4000/${thumbail}`;
  }

  /**
   * Track by function for ngFor performance
   */
  trackByProductId(index: number, product: any): string {
    return product._id || index.toString();
  }

  /**
   * Open product detail dialog
   */
  onProductClick(product: any) {
    const dialogRef = this.dialog.open(ProductDetailDialog, {
      width: '90vw',
      maxWidth: '1200px',
      maxHeight: '90vh',
      disableClose: false,
      autoFocus: false,
      data: {
        productId: product._id,
        productImage: product.image,
      },
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result) {
        if (result.action === 'addToCart') {
          console.log('Add to cart:', result.data);
          // Handle add to cart logic
        } else if (result.action === 'buyNow') {
          console.log('Buy now:', result.data);
          // Handle buy now logic
        }
      }
    });
  }

  onImageClick() {
    // Please sync "Tin tuc chi tiet" to the project
  }

  onOrderClick() {
    this.dialog.open(ChonMon, {
      disableClose: false,
      autoFocus: true,
      height: '705px',
    });
  }
}
