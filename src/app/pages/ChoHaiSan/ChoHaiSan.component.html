<div class="container">
  <main class="cho-hai-san-inner">
    <div class="frame-parent">
      <div class="frame-group align-items-center">
        <div class="frame-wrapper">
          <div class="rectangle-parent">
            <div class="frame-child"></div>
            <div class="frame-item"></div>
            <div class="frame-inner"></div>
          </div>
        </div>
        <section class="product-images">
          <img class="image-icon" alt="" src="assets/<EMAIL>" (click)="onImageClick()" />
        </section>
        <div class="frame-container">
          <div class="frame-div w-100">
            <div class="ch-hi-sn-lng-nui-bin-v-wrapper">
              <h3 class="ch-hi-sn">CHỢ HẢI SẢN - LÀNG NUÔI BIỂN VÂN ĐỒN</h3>
            </div>
            <div class="frame-parent1 justify-content-center">
              <!-- Tất cả danh mục -->
              <button
                class="rectangle-group align-items-center justify-content-center category-btn"
                [class.active]="selectedCategoryId === ''"
                (click)="onCategorySelect('')">
                <div class="rectangle-div"></div>
                <div class="thc-phm-ti">TẤT CẢ</div>
              </button>

              <!-- Danh mục từ API -->
              <button
                *ngFor="let category of categories"
                class="rectangle-group align-items-center justify-content-center category-btn"
                [class.active]="selectedCategoryId === category._id"
                (click)="onCategorySelect(category._id)">
                <div class="rectangle-div"></div>
                <div class="thc-phm-ti">{{category.name}}</div>
              </button>
            </div>
          </div>
        </div>
        <!-- Loading indicator -->
        <div *ngIf="loading && products.length === 0" class="loading-container text-center p-4">
          <div class="spinner-border" role="status">
            <span class="visually-hidden">Đang tải...</span>
          </div>
          <p class="mt-2">Đang tải sản phẩm...</p>
        </div>

        <!-- Products grid -->
        <div class="frame-parent2" *ngIf="!loading || products.length > 0">
          <app-product-item
            *ngFor="let product of products; trackBy: trackByProductId"
            [image]="product.image"
            [price]="product.price"
            [name]="product.name"
            [priceDiscount]="product.priceDiscount"
            [discount]="product.discount"
            (click)="onProductClick(product)"
            (onBuyClick)="onOrderClick()"
            style="cursor: pointer;">
          </app-product-item>
        </div>

        <!-- Empty state -->
        <div *ngIf="!loading && products.length === 0" class="empty-state text-center p-4">
          <i class="fas fa-box-open fa-3x mb-3 text-muted"></i>
          <h4>Không tìm thấy sản phẩm</h4>
          <p class="text-muted">Thử chọn danh mục khác hoặc thử lại sau</p>
        </div>
      </div>
      <!-- Load More Button -->
      <button
        *ngIf="hasMore && products.length > 0"
        class="frame-wrapper1"
        (click)="onLoadMore()"
        [disabled]="loading">
        <div class="rectangle-parent2">
          <div class="frame-child3"></div>
          <div class="thc-phm-ti">
            <span *ngIf="!loading">XEM THÊM SẢN PHẨM</span>
            <span *ngIf="loading">
              <div class="spinner-border spinner-border-sm me-2" role="status"></div>
              ĐANG TẢI...
            </span>
          </div>
        </div>
      </button>

      <!-- End message -->
      <div *ngIf="!hasMore && products.length > 0" class="text-center p-4">
        <p class="text-muted">Đã hiển thị tất cả sản phẩm</p>
      </div>
    </div>
  </main>

</div>
