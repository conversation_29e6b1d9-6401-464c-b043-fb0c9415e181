import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { IconsModule } from '../../icons.module';
import {MatIcon} from "@angular/material/icon";
import {MatSuffix} from "@angular/material/form-field";

export interface CartItem {
  _id: string;
  name: string;
  price: number;
  priceOld?: number;
  thumbail: string;
  selectedOption: string;
  quantity: number;
  note: string;
}

export interface Store {
  _id: string;
  name: string;
  address: string;
  phone: string;
}

export interface CustomerInfo {
  _id: string;
  name: string;
  phone: string;
  address: string;
}

export interface PaymentMethod {
  _id: string;
  name: string;
  description: string;
}

export interface Bank {
  _id: string;
  name: string;
  logo: string;
  code: string;
}

@Component({
  selector: 'app-pay',
  standalone: true,
  imports: [CommonModule, FormsModule, MatCheckboxModule, IconsModule, MatIcon, MatSuffix],
  templateUrl: './pay.component.html',
  styleUrls: ['./pay.component.scss'],
})
export class PayComponent implements OnInit {
  loading = false;

  // Form data
  selectedStoreIndex = 0;
  selectedCustomerIndex = 0;
  selectedPaymentIndex = 0;
  selectedBankIndex = 0;
  discountCode = '';
  discountPercent = 0;

  // Cart items
  cartItems: CartItem[] = [
    {
      _id: '1',
      name: 'Tên món ăn',
      price: 80000,
      priceOld: 100000,
      thumbail: 'assets/images/products/s1.jpg',
      selectedOption: 'Op1',
      quantity: 1,
      note: 'Ghi nội dung ghi chú để chúng tôi phục vụ tốt hơn'
    },
    {
      _id: '2',
      name: 'Tên món ăn',
      price: 80000,
      priceOld: 100000,
      thumbail: 'assets/images/products/s2.jpg',
      selectedOption: 'Op1',
      quantity: 1,
      note: 'Ghi nội dung ghi chú để chúng tôi phục vụ tốt hơn'
    }
  ];

  // Store options
  stores: Store[] = [
    {
      _id: '1',
      name: 'Tên cửa hàng - cơ sở',
      address: 'Số 134 Trường Định, Phường Trường Định, Quận Hai Bà Trưng, Thành phố Hà Nội',
      phone: '0912 345 678'
    },
    {
      _id: '2',
      name: 'Tên cửa hàng - cơ sở',
      address: 'Số 134 Trường Định, Phường Trường Định, Quận Hai Bà Trưng, Thành phố Hà Nội',
      phone: '0912 345 678'
    }
  ];

  // Customer info options
  customerInfos: CustomerInfo[] = [
    {
      _id: '1',
      name: 'Nguyễn Văn A',
      phone: '0912 345 678',
      address: 'Số 134 Trường Định, Phường Trường Định, Quận Hai Bà Trưng, Thành phố Hà Nội'
    },
    {
      _id: '2',
      name: 'Nguyễn Văn A',
      phone: '0912 345 678',
      address: 'Số 134 Trường Định, Phường Trường Định, Quận Hai Bà Trưng, Thành phố Hà Nội'
    }
  ];

  // Payment methods
  paymentMethods: PaymentMethod[] = [
    {
      _id: '1',
      name: 'Chuyển khoản',
      description: 'Chuyển khoản'
    },
    {
      _id: '2',
      name: 'COD-Thanh toán bằng tiền mặt khi nhận hàng',
      description: 'COD-Thanh toán bằng tiền mặt khi nhận hàng'
    },
  ];

  // Banks
  banks: Bank[] = [
    {
      _id: '1',
      name: 'Vietcombank',
      logo: 'assets/images/banks/vcb.png',
      code: 'VCB'
    },
    {
      _id: '2',
      name: 'Techcombank',
      logo: 'assets/images/banks/tcb.png',
      code: 'TCB'
    },
    {
      _id: '3',
      name: 'BIDV',
      logo: 'assets/images/banks/bidv.png',
      code: 'BIDV'
    }
  ];

  constructor() {}

  ngOnInit(): void {
    // Initialize component
  }

  /**
   * Xử lý lỗi hình ảnh
   */
  handleImageError(event: Event): void {
    const imgElement = event.target as HTMLImageElement;
    imgElement.src = 'assets/images/placeholder.svg';
    imgElement.classList.add('img-error');
  }

  /**
   * Tăng số lượng món
   */
  increaseQuantity(index: number): void {
    this.cartItems[index].quantity++;
  }

  /**
   * Giảm số lượng món
   */
  decreaseQuantity(index: number): void {
    if (this.cartItems[index].quantity > 1) {
      this.cartItems[index].quantity--;
    }
  }

  /**
   * Xóa món khỏi giỏ hàng
   */
  removeItem(index: number): void {
    this.cartItems.splice(index, 1);
  }

  /**
   * Áp dụng mã giảm giá
   */
  applyDiscountCode(): void {
    // Mock discount logic
    if (this.discountCode === 'DISCOUNT10') {
      this.discountPercent = 10;
    } else if (this.discountCode === 'DISCOUNT20') {
      this.discountPercent = 20;
    } else {
      this.discountPercent = 0;
    }
  }

  /**
   * Tính tổng tiền
   */
  getSubTotal(): number {
    return this.cartItems.reduce((total, item) => total + (item.price * item.quantity), 0);
  }

  /**
   * Tính tiền giảm giá
   */
  getDiscountAmount(): number {
    return (this.getSubTotal() * this.discountPercent) / 100;
  }

  /**
   * Tính tổng tiền cuối cùng
   */
  getTotalAmount(): number {
    return this.getSubTotal() - this.getDiscountAmount();
  }

  /**
   * Format giá tiền
   */
  formatPrice(price: number): string {
    return new Intl.NumberFormat('vi-VN', {
      style: 'currency',
      currency: 'VND'
    }).format(price);
  }

  /**
   * Thanh toán
   */
  processPayment(): void {
    const orderData = {
      store: this.stores[this.selectedStoreIndex],
      customer: this.customerInfos[this.selectedCustomerIndex],
      paymentMethod: this.paymentMethods[this.selectedPaymentIndex],
      items: this.cartItems,
      discountCode: this.discountCode,
      discountPercent: this.discountPercent,
      subTotal: this.getSubTotal(),
      discountAmount: this.getDiscountAmount(),
      totalAmount: this.getTotalAmount()
    };

    console.log('Processing payment:', orderData);
    // Implement payment logic here
  }
}
