#!/bin/bash

# Cấu hình
PROJECT_DIR=~/source/langnoibienvandon-frontend
SERVICE_NAME=lbvd-ui
IMAGE_NAME=lbvd-ui

echo "📁 Moving to project directory: $PROJECT_DIR"
cd "$PROJECT_DIR" || { echo "❌ Failed to cd into $PROJECT_DIR"; exit 1; }

echo "📥 Pulling latest code from Git"
if git pull; then
    echo "✅ Git pull completed"
else
    echo "⚠️ Git pull failed, continuing anyway..."
fi

echo "🛑 Stopping service: $SERVICE_NAME"
docker compose stop $SERVICE_NAME

echo "🧹 Removing container: $SERVICE_NAME"
docker compose rm -f $SERVICE_NAME

echo "🗑️ Removing image: $IMAGE_NAME"
IMAGE_ID=$(docker images "$IMAGE_NAME" -q)
if [ -n "$IMAGE_ID" ]; then
    docker rmi "$IMAGE_ID"
else
    echo "ℹ️ No image found for $IMAGE_NAME"
fi

echo "🔨 Building service: $SERVICE_NAME"
if docker compose build $SERVICE_NAME; then
    echo "✅ Build completed"
else
    echo "⚠️ Build failed, continuing to run container anyway..."
fi

echo "🚀 Starting service: $SERVICE_NAME"
if docker compose up -d $SERVICE_NAME; then
    echo "✅ Service is up and running"
else
    echo "❌ Failed to start service"
fi

echo "🏁 Script finished!"
