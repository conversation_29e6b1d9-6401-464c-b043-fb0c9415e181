.booking-dialog {
  width: 100%;
  border-radius: 16px;
  padding: 16px;
  overflow: auto;
  background-color: #fafbfc;

  // Responsive padding
  @media (min-width: 576px) {
    padding: 20px;
  }

  @media (min-width: 768px) {
    padding: 24px;
  }

  .dialog-header {
    padding: 16px 0 20px 0;
    color: white;
    margin: -16px 0 0 0;
    position: relative;

    @media (min-width: 576px) {
      padding: 20px 0 24px 0;
      margin: -20px 0 0 0;
    }

    @media (min-width: 768px) {
      padding: 20px 0 24px 0;
      margin: -24px 0 0 0;
    }

    h2 {
      font-size: 16px;
      font-weight: 700;
      letter-spacing: 0.5px;
      position: relative;
      z-index: 1;

      @media (min-width: 576px) {
        font-size: 18px;
      }

      @media (min-width: 768px) {
        font-size: 20px;
      }

      .header-icon {
        font-size: 20px;
        color: #fbbf24;

        @media (min-width: 576px) {
          font-size: 24px;
        }

        @media (min-width: 768px) {
          font-size: 28px;
        }
      }
    }

    .close-button {
      color: silver;
      width: 36px;
      height: 36px;

      @media (min-width: 768px) {
        width: 40px;
        height: 40px;
      }

      mat-icon {
        font-size: 20px;

        @media (min-width: 768px) {
          font-size: 24px;
        }
      }
    }
  }

  .dialog-content {
    padding: 0 15px;
    max-height: 70vh;
    background: #fafbfc;

    // Responsive max-height
    @media (max-width: 767px) {
      max-height: 70vh;
    }

    .loading-container {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      padding: 40px 20px;
      background: white;
      border-radius: 12px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

      @media (min-width: 768px) {
        padding: 60px 40px;
      }

      mat-spinner {
        margin-bottom: 20px;
      }

      p {
        margin: 0;
        color: #6b7280;
        font-size: 14px;
        font-weight: 500;
        text-align: center;

        @media (min-width: 768px) {
          font-size: 16px;
        }
      }
    }

    .booking-form {
      // Bootstrap classes handle spacing, remove custom form-row styles
      // Keep only specific overrides if needed

      // Decreasing z-index for form rows from top to bottom
      .row {
        position: relative;
      }

      .row:nth-child(1) { z-index: 100; }
      .row:nth-child(2) { z-index: 99; }
      .row:nth-child(3) { z-index: 98; }
      .row:nth-child(4) { z-index: 97; }
      .row:nth-child(5) { z-index: 96; }
      .row:nth-child(6) { z-index: 95; }
      .row:nth-child(7) { z-index: 94; }
      .row:nth-child(8) { z-index: 93; }
      .row:nth-child(9) { z-index: 92; }
      .row:nth-child(10) { z-index: 91; }

      // Alternative approach for many rows
      .form-row {
        position: relative;

        &:nth-child(1) { z-index: 100; }
        &:nth-child(2) { z-index: 99; }
        &:nth-child(3) { z-index: 98; }
        &:nth-child(4) { z-index: 97; }
        &:nth-child(5) { z-index: 96; }
        &:nth-child(6) { z-index: 95; }
        &:nth-child(7) { z-index: 94; }
        &:nth-child(8) { z-index: 93; }
        &:nth-child(9) { z-index: 92; }
        &:nth-child(10) { z-index: 91; }
      }

      // Ensure form controls don't create stacking context issues
      app-text-input,
      app-select-input,
      app-number-input {
        position: relative;
        z-index: inherit;
      }
    }
  }

  .dialog-actions {
    padding: 20px 0 0 0;
    padding: 0;
    display: flex;
    margin: 0 auto;
    width: 100%;
    justify-content: center;
    flex-direction: column;
    align-content: center;
    background: #fafbfc;

    @media (min-width: 576px) {
      padding: 24px 0 0 0;
      margin: 0 -20px -20px -20px;
    }

    @media (min-width: 768px) {
      padding: 24px 0 0 0;
      margin: 0;
    }

    .submit-button {
      height: 48px;
      font-size: 14px;
      font-weight: 700;
      letter-spacing: 0.5px;
      background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
      border-radius: 8px;
      box-shadow: 0 4px 12px rgba(37, 99, 235, 0.3);
      transition: all 0.3s ease;

      @media (min-width: 576px) {
        height: 52px;
        font-size: 15px;
        border-radius: 10px;
      }

      @media (min-width: 768px) {
        height: 56px;
        font-size: 16px;
        border-radius: 12px;
      }

      .button-spinner {
        // Bootstrap me-2 class handles margin
      }

      &:hover:not(:disabled) {
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(37, 99, 235, 0.4);
      }

      &:disabled {
        background: #d1d5db !important;
        color: #9ca3af !important;
        box-shadow: none !important;
        transform: none !important;
      }
    }
  }
}

// Global styles
::ng-deep {
  .booking-dialog-container {
    .mat-mdc-dialog-container {
      border-radius: 12px;
      overflow: hidden;
      box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);

      @media (min-width: 768px) {
        border-radius: 16px;
      }
    }
  }

  // Mobile full width dialog
  .booking-dialog-container-mobile {
    .mat-mdc-dialog-container {
      width: 100vw !important;
      max-width: 100vw !important;
      height: 90vh !important;
      max-height: 90vh !important;
      margin: 0 !important;
      border-radius: 0 !important;
      margin-top: 90px !important;
      .booking-dialog {
        height: 100%;
        overflow-y: auto;
        border-radius: 0;
      }
    }
  }

  .success-snackbar {
    background-color: #10b981 !important;
    color: white !important;

    .mat-mdc-snack-bar-label {
      font-weight: 600;
    }
  }

  .error-snackbar {
    background-color: #ef4444 !important;
    color: white !important;

    .mat-mdc-snack-bar-label {
      font-weight: 600;
    }
  }

  // Custom datepicker styles
  .mat-datepicker-popup {
    border-radius: 12px;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
  }

  .mat-calendar {
    border-radius: 12px;
  }

  .mat-calendar-header {
    background-color: #2563eb;
    color: white;
    border-radius: 12px 12px 0 0;
  }

  .mat-calendar-body-selected {
    background-color: #2563eb;
    color: white;
  }

  .mat-calendar-body-today:not(.mat-calendar-body-selected) {
    border-color: #2563eb;
  }
}
::ng-deep .mat-mdc-form-field-hint-wrapper, .mat-mdc-form-field-error-wrapper {
  padding: 0!important;
}
