:host {
  display: block;
  width: 100%;

  ::ng-deep .mat-mdc-form-field {
    margin-bottom: 0;

    .mat-mdc-text-field-wrapper {

      &:hover {
        border-color: #cbd5e1;
        background-color: #f1f5f9;
      }

      &.mdc-text-field--focused {
        border-color: #2563eb;
        background-color: white;
        box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
      }
    }

    .mat-mdc-form-field-flex {
      height: 48px;
      align-items: center;
    }

    .mat-mdc-form-field-infix {
      padding: 0;
      min-height: auto;
    }

    .mat-mdc-form-field-icon-prefix,
    .mat-mdc-form-field-icon-suffix {
      color: #6b7280;
      margin: 0 8px;
    }

    .mat-mdc-form-field-subscript-wrapper {
      .mat-mdc-form-field-hint,
      .mat-mdc-form-field-error {
        font-size: 13px;
        font-weight: 500;
      }

      .mat-mdc-form-field-error {
        color: #ef4444;
      }

      .mat-mdc-form-field-hint {
        color: #6b7280;
      }
    }

    .mdc-floating-label {
      font-size: 14px;
      font-weight: 600;
      color: #374151;

      &.mdc-floating-label--float-above {
        color: #2563eb;
      }
    }
  }
}

.w-100 {
  width: 100%;
}

.number-input-container {
  display: flex;
  align-items: center;
  width: 100%;
  gap: 8px;

  .number-button {
    width: 36px;
    height: 36px;
    min-width: 36px;
    border-radius: 8px;
    background-color: #f3f4f6;
    border: 1px solid #d1d5db;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0;
    line-height: normal;

    &:hover:not(:disabled) {
      background-color: #2563eb;
      border-color: #2563eb;
      color: white;
      transform: scale(1.05);
    }

    mat-icon {
      font-size: 18px;
      width: 18px;
      height: 18px;
      display: flex;
      align-items: center;
      justify-content: center;
      margin: 0;
    }

    &:disabled {
      opacity: 0.4;
      cursor: not-allowed;
      background-color: #f9fafb;
    }
  }

  .number-input {
    text-align: center;
    flex: 1;
    font-size: 15px;
    font-weight: 600;
    color: #1f2937;
    border: none;
    background: transparent;
    outline: none;

    // Hide default number input spinners
    &::-webkit-outer-spin-button,
    &::-webkit-inner-spin-button {
      -webkit-appearance: none;
      margin: 0;
    }

    &[type=number] {
      -moz-appearance: textfield;
    }
  }
}

// Responsive adjustments
@media (max-width: 600px) {
  .number-input-container {
    .number-button {
      width: 28px;
      height: 28px;
      min-width: 28px;
      line-height: 28px;

      mat-icon {
        font-size: 16px;
        width: 16px;
        height: 16px;
      }
    }
  }
}


.mat-icon {
  padding: 0!important;
}
