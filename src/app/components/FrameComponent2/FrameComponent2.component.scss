.frame-child7,
.image-icon11 {
  position: relative;
  display: none;
}
.image-icon11 {
  height: 287px;
  width: 302px;
  border-radius: var(--br-10);
  object-fit: cover;
}
.frame-child7 {
  height: 14px;
  width: 55px;
  border-radius: var(--br-3);
  background: linear-gradient(135deg, #00e05a, #00d7e4);
}
.frame-icon,
.free-ship {
  position: relative;
  z-index: 1;
}
.frame-icon {
  height: 10px;
  width: 10px;
  overflow: hidden;
  flex-shrink: 0;
}
.free-ship {
  letter-spacing: -0.5px;
  text-shadow: 0 1px 1px rgba(0, 0, 0, 0.4);
}
.group-div {
  border-radius: var(--br-3);
  background: linear-gradient(135deg, #00e05a, #00d7e4);
  display: flex;
  flex-direction: row;
  align-items: flex-start;
  justify-content: flex-start;
  padding: var(--padding-2) var(--padding-3) 0;
  gap: var(--gap-2);
  z-index: 1;
}
.frame-child8 {
  height: 14px;
  width: 38px;
  position: relative;
  border-radius: var(--br-3);
  background: linear-gradient(135deg, var(--color-crimson-100), #ac3137);
  display: none;
}
.frame-icon1,
.sale {
  position: relative;
  z-index: 2;
}
.frame-icon1 {
  height: 10px;
  width: 10px;
  overflow: hidden;
  flex-shrink: 0;
}
.sale {
  letter-spacing: -0.5px;
  text-shadow: 0 1px 1px rgba(0, 0, 0, 0.4);
}
.image-group,
.rectangle-parent5 {
  display: flex;
  flex-direction: row;
  align-items: flex-start;
}
.rectangle-parent5 {
  width: 38px;
  border-radius: var(--br-3);
  background: linear-gradient(135deg, var(--color-crimson-100), #ac3137);
  justify-content: flex-start;
  padding: var(--padding-2) var(--padding-3) 0;
  box-sizing: border-box;
  gap: var(--gap-2);
  z-index: 1;
}
.image-group {
  align-self: stretch;
  border-radius: var(--br-10);
  justify-content: flex-end;
  padding: 261px var(--padding-12) var(--padding-8);
  gap: var(--gap-4);
  background-image: url(../../../assets/<EMAIL>);
  background-size: cover;
  background-repeat: no-repeat;
  background-position: top;
}
.div,
.tm-hm-bng {
  position: relative;
}
.div {
  text-decoration: line-through;
  display: inline-block;
  min-width: 51px;
}
.blank-spots {
  position: relative;
  color: var(--color-crimson-200);
}
.decimal-values,
.decimal-values-parent,
.parent {
  display: flex;
  align-items: flex-start;
  justify-content: flex-start;
}
.decimal-values {
  flex-direction: row;
  gap: var(--gap-16);
}
.decimal-values-parent,
.parent {
  flex-direction: column;
}
.decimal-values-parent {
  gap: 9px;
  font-size: var(--font-size-12);
  font-family: var(--font-body);
}
.parent {
  padding: 0 0 var(--padding-6);
  gap: var(--gap-4);
}
.frame-child9 {
  height: 40px;
  width: 167px;
  position: relative;
  border-radius: var(--br-4);
  background-color: var(--color-steelblue-300);
  display: none;
}
.mua-ngay {
  flex: 1;
  position: relative;
  font-size: 13px;
  font-family: var(--font-body);
  color: var(--color-white);
  text-align: center;
  z-index: 1;
}
.rectangle-parent6 {
  cursor: pointer;
  border: 0;
  padding: var(--padding-12) 46px 12.2px;
  background-color: var(--color-steelblue-300);
  border-radius: var(--br-4);
  display: flex;
  flex-direction: row;
  align-items: flex-start;
  justify-content: flex-start;
  box-sizing: border-box;
}
.rectangle-parent6:hover {
  background-color: var(--color-steelblue-200);
}
.frame-parent5,
.tm-hm-bng-hp-ru-vang-parent {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: flex-start;
}
.tm-hm-bng-hp-ru-vang-parent {
  gap: 7px;
  font-size: var(--font-size-16);
  color: var(--color-gray-100);
  font-family: var(--font-body);
}
.frame-parent5 {
  width: 302px;
  gap: var(--gap-20);
  min-width: 287px;
  text-align: left;
  font-size: var(--font-size-8);
  color: var(--color-white);
  font-family: var(--font-fugaz-one);
  margin-bottom: var(--gap-48);
}
@media screen and (max-width: 450px) {
  .image-group {
    flex-wrap: wrap;
  }
}

// Hover effects for product cards
.frame-parent5 {
  transition: all 0.3s ease;
  cursor: pointer;

  &:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);

    .image-group {
      .image-overlay {
        opacity: 1;
      }
    }
  }
}

// Image overlay for hover effect
.image-group {
  position: relative;
  overflow: hidden;

  .image-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.2);
    opacity: 0;
    transition: opacity 0.3s ease;
    border-radius: var(--br-10);
    z-index: 0;
  }

  // Ensure badges stay on top
  .group-div,
  .rectangle-parent5 {
    position: relative;
    z-index: 2;
  }
}
