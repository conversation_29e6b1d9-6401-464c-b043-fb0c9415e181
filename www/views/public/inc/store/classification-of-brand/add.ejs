<div class="content_boxed">
    <section class="page_title">
        <div class="container">
            <div class="row">
                <div class="col-md-12">
                    <div class="page_title_container">
                        <h1>
                            <a href="/store/service-of-brand/<%- classifyServiceType %>">
                                <img src="/template/ui/img/arrow-left.png" alt=""></a> Thêm mới đặt bàn
                        </h1>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <section class="sec_portfolio">
        <div class="container">
            <div class="row">
                <div class="col-md-12">
                    <div class="search_portfolio form_schedule edit">
                        <form class="container" id="form-add-service">
                            <div class="row">
                                <div class="col-md-6 col-sm-6 col-xs-12">
                                    <div class="input_field schedule_field">
                                        <label for="">Tên dịch vụ</label>
                                        <input type="text" name="name" placeholder="Nhập tên dịch vụ">
                                    </div>
                                </div>
                                <div class="col-md-3 col-sm-6 col-xs-12">
                                    <div class="input_field schedule_field">
                                        <label for="">Thương hiệu</label>
                                        <div class="select_form">
                                            <select class="" name="storeId">
                                                <option value="null">[[---------Chọn thương hiệu --------]]</option>
                                                <% brands.forEach(item=>{ %>
                                                    <option value="<%- item._id %>"><%- item.name %> </option>
                                                <% }) %>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-12 col-sm-12 col-xs-12">
                                    <div class="input_field schedule_field">
                                        <label for="">Cơ sở</label>
                                        <div class="select_form">
                                            <select class="" name="branchId"></select>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-12">
                                    <div class="input_field schedule_field">
                                        <label for="">Mô tả ngắn</label>
                                        <textarea name="shortDes" placeholder="Nhập nội dung" style="height: 100px"></textarea>
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-12">
                                    <div class="input_field schedule_field">
                                        <label for="">Mô tả</label>
                                        <textarea name="description" placeholder="Nhập nội dung" style="height: 300px"></textarea>
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="input_field schedule_field">
                                        <label for="">Giá chung</label>
                                        <input name="price" placeholder="Nhập giá" type="text" value="" data-type="currency">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="input_field schedule_field">
                                        <label for="">Tình trạng</label>
                                        <div class="select_form">
                                            <select class="" name="typeService">
                                                <option value="all" disabled>Chọn tình trạng</option>
                                                <option value="0">Dừng phục vụ</option>
                                                <option value="1" selected>Đang phục vụ</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-12"><label>Thêm phân loại bảng giá</label></div>
                            </div>
                            <div class="row" id="classify-list"></div>
                            <div class="row">
                                <div class="col-md-4"></div>
                                <div class="col-md-4">
                                    <div class="input_field schedule_field">
                                        <a class="button-classify" style="background: white;border: solid 2px #1969EF; color: #1969EF;" onclick="addClassify()"><img src="/template/ui/img/plus-blue.svg" alt=""> Thêm phân loại</a>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-3">
                                    <div class="input_field schedule_field upload_img_product">
                                        <label for="">Ảnh bìa</label>
                                        <div class="upload_container img_cover">
                                            <div class="upfile">
                                                <label for="upload-service-0"><img src="/template/ui/img/upload.png" alt=""></label>
                                                <input class="upload-new-picture upfile-input" type="file" name="myfile"
                                                       id="upload-service-0" accept="image/x-png,image/gif,image/jpeg"
                                                       class="">
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </form>
                        <section>
                            <div id="dropzone">
                                <form class="dropzone needsclick" id="upload-images" action="/">
                                    <div class="dz-message needsclick">
                                        Kéo ảnh thả ảnh vào hoặc<span
                                                class="btn btn-link">ấn vào đây để tải ảnh</span></span>.
                                        <span class="note needsclick">( Kích thước tối đa <strong>3MB</strong>)</span>
                                    </div>
                                </form>
                            </div>
                        </section>
                        <div class="button_submit_form">
                            <a onclick="goBack()">Quay lại</a>
                            <button onclick="submitForm()" name="save">Lưu</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
</div>
<script>
    function goBack() {
        window.history.back();
    }
    var classifyServiceType = '<%- classifyServiceType %>';
</script>
<script type="text/javascript" src="/template/store/add-service-of-brand.js?v=<%- cacheVersion %>"></script>
