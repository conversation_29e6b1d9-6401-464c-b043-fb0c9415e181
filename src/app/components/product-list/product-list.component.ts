import { Component, OnInit, <PERSON><PERSON><PERSON>roy } from '@angular/core';
import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { ProductService } from '../../services/product.service';
import { Product, Category, SearchParams } from '../../interfaces/product.interface';

@Component({
  selector: 'app-product-list',
  templateUrl: './product-list.component.html',
  styleUrls: ['./product-list.component.scss']
})
export class ProductListComponent implements OnInit, OnDestroy {
  private destroy$ = new Subject<void>();
  
  products: Product[] = [];
  categories: Category[] = [];
  loading = false;
  hasMore = true;
  
  // Filter parameters
  selectedCategoryId: string = '';
  searchText: string = '';
  currentOrder: string = 'sales';
  currentShipping: string = '';
  currentPrice: string = '';
  currentRate: number = 0;
  
  // Pagination
  currentPage = 1;
  limit = 10;

  constructor(private productService: ProductService) {}

  ngOnInit(): void {
    this.initializeSubscriptions();
    this.loadCategories();
    this.loadProducts();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  private initializeSubscriptions(): void {
    // Subscribe to products
    this.productService.products$
      .pipe(takeUntil(this.destroy$))
      .subscribe(products => {
        this.products = products;
      });

    // Subscribe to loading state
    this.productService.loading$
      .pipe(takeUntil(this.destroy$))
      .subscribe(loading => {
        this.loading = loading;
      });

    // Subscribe to hasMore state
    this.productService.hasMore$
      .pipe(takeUntil(this.destroy$))
      .subscribe(hasMore => {
        this.hasMore = hasMore;
      });
  }

  private loadCategories(): void {
    this.productService.getCategories()
      .pipe(takeUntil(this.destroy$))
      .subscribe(categories => {
        this.categories = categories;
      });
  }

  private loadProducts(): void {
    const params: SearchParams = {
      search: this.searchText,
      type: 0, // 0 = sản phẩm
      page: 1,
      categoryId: this.selectedCategoryId || undefined,
      order: this.currentOrder,
      shipping: this.currentShipping || undefined,
      price: this.currentPrice || undefined,
      rate: this.currentRate || undefined,
      limit: this.limit
    };

    this.productService.searchProducts(params)
      .pipe(takeUntil(this.destroy$))
      .subscribe(response => {
        if (response.error) {
          console.error('Error loading products:', response.message);
        }
      });
  }

  /**
   * Load more products
   */
  onLoadMore(): void {
    if (!this.loading && this.hasMore) {
      this.productService.loadMoreProducts()
        .pipe(takeUntil(this.destroy$))
        .subscribe(response => {
          if (response.error) {
            console.error('Error loading more products:', response.message);
          }
        });
    }
  }

  /**
   * Filter by category
   */
  onCategorySelect(categoryId: string): void {
    this.selectedCategoryId = categoryId;
    this.productService.resetState();
    this.loadProducts();
  }

  /**
   * Search products
   */
  onSearch(): void {
    this.productService.resetState();
    this.loadProducts();
  }

  /**
   * Clear all filters
   */
  onClearFilters(): void {
    this.selectedCategoryId = '';
    this.searchText = '';
    this.currentOrder = 'sales';
    this.currentShipping = '';
    this.currentPrice = '';
    this.currentRate = 0;
    this.productService.resetState();
    this.loadProducts();
  }

  /**
   * Sort products
   */
  onSortChange(order: string): void {
    this.currentOrder = order;
    this.productService.resetState();
    this.loadProducts();
  }

  /**
   * Filter by shipping
   */
  onShippingChange(shipping: string): void {
    this.currentShipping = shipping;
    this.productService.resetState();
    this.loadProducts();
  }

  /**
   * Filter by price range
   */
  onPriceChange(price: string): void {
    this.currentPrice = price;
    this.productService.resetState();
    this.loadProducts();
  }

  /**
   * Filter by rating
   */
  onRateChange(rate: number): void {
    this.currentRate = rate;
    this.productService.resetState();
    this.loadProducts();
  }

  /**
   * Format price
   */
  formatPrice(price: number): string {
    return new Intl.NumberFormat('vi-VN', {
      style: 'currency',
      currency: 'VND'
    }).format(price);
  }

  /**
   * Get product image URL
   */
  getProductImageUrl(thumbail: string): string {
    if (thumbail && !thumbail.startsWith('http')) {
      return `http://localhost:4000/${thumbail}`;
    }
    return thumbail || 'assets/images/no-image.png';
  }

  /**
   * Track by function for ngFor
   */
  trackByProductId(index: number, product: Product): string {
    return product._id;
  }

  /**
   * Track by function for categories
   */
  trackByCategoryId(index: number, category: Category): string {
    return category._id;
  }
}
