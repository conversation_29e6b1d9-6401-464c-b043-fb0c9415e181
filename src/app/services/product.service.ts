import { Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { Observable, BehaviorSubject } from 'rxjs';
import { map, catchError } from 'rxjs/operators';
import { of } from 'rxjs';
import { 
  Product, 
  Category, 
  SearchProductResponse, 
  SearchParams, 
  ApiResponse 
} from '../interfaces/product.interface';

@Injectable({
  providedIn: 'root'
})
export class ProductService {
  private baseUrl = 'http://localhost:4000/user/api';
  
  // BehaviorSubject để quản lý state của danh sách sản phẩm
  private productsSubject = new BehaviorSubject<Product[]>([]);
  public products$ = this.productsSubject.asObservable();
  
  private loadingSubject = new BehaviorSubject<boolean>(false);
  public loading$ = this.loadingSubject.asObservable();
  
  private hasMoreSubject = new BehaviorSubject<boolean>(true);
  public hasMore$ = this.hasMoreSubject.asObservable();
  
  // State cho pagination
  private currentPage = 1;
  private totalPages = 1;
  private currentSearchParams: SearchParams = {};

  constructor(private http: HttpClient) {}

  /**
   * Tìm kiếm sản phẩm với các tham số
   */
  searchProducts(params: SearchParams): Observable<SearchProductResponse> {
    this.loadingSubject.next(true);
    
    // Reset state khi tìm kiếm mới
    this.currentPage = params.page || 1;
    this.currentSearchParams = { ...params };
    
    let httpParams = new HttpParams();
    
    // Thêm các tham số vào request
    if (params.search !== undefined) httpParams = httpParams.set('search', params.search);
    if (params.type !== undefined) httpParams = httpParams.set('type', params.type.toString());
    if (params.page !== undefined) httpParams = httpParams.set('page', params.page.toString());
    if (params.categoryId) httpParams = httpParams.set('categoryId', params.categoryId);
    if (params.order) httpParams = httpParams.set('order', params.order);
    if (params.shipping) httpParams = httpParams.set('shipping', params.shipping);
    if (params.price) httpParams = httpParams.set('price', params.price);
    if (params.rate !== undefined) httpParams = httpParams.set('rate', params.rate.toString());
    if (params.limit !== undefined) httpParams = httpParams.set('limit', params.limit.toString());
    if (params.typeShip !== undefined) httpParams = httpParams.set('typeShip', params.typeShip.toString());

    return this.http.get<SearchProductResponse>(`${this.baseUrl}/search-v2`, { params: httpParams })
      .pipe(
        map(response => {
          this.loadingSubject.next(false);
          
          if (response.error === false && response.data) {
            this.totalPages = response.data.totalPage;
            
            // Nếu là trang đầu tiên, thay thế danh sách
            if (this.currentPage === 1) {
              this.productsSubject.next(response.data.result);
            } else {
              // Nếu là load more, thêm vào danh sách hiện tại
              const currentProducts = this.productsSubject.value;
              this.productsSubject.next([...currentProducts, ...response.data.result]);
            }
            
            // Cập nhật hasMore
            this.hasMoreSubject.next(this.currentPage < this.totalPages);
          }
          
          return response;
        }),
        catchError(error => {
          this.loadingSubject.next(false);
          console.error('Error searching products:', error);
          return of({
            error: true,
            message: 'Có lỗi xảy ra khi tìm kiếm sản phẩm',
            data: { result: [], totalPage: 0 }
          } as SearchProductResponse);
        })
      );
  }

  /**
   * Load thêm sản phẩm (pagination)
   */
  loadMoreProducts(): Observable<SearchProductResponse> {
    if (this.currentPage >= this.totalPages) {
      return of({
        error: false,
        message: 'Không còn sản phẩm nào để tải',
        data: { result: [], totalPage: this.totalPages }
      } as SearchProductResponse);
    }

    this.currentPage++;
    const params = { ...this.currentSearchParams, page: this.currentPage };
    
    return this.searchProducts(params);
  }

  /**
   * Lấy sản phẩm theo danh mục
   */
  getProductsByCategory(categoryId: string, page: number = 1, limit: number = 10): Observable<SearchProductResponse> {
    const params: SearchParams = {
      search: '',
      type: 0, // 0 = sản phẩm
      page,
      categoryId,
      limit
    };
    
    return this.searchProducts(params);
  }

  /**
   * Lấy danh sách danh mục
   */
  getCategories(): Observable<Category[]> {
    return this.http.get<ApiResponse<Category[]>>(`${this.baseUrl}/trang-chu.html`)
      .pipe(
        map(response => {
          if (response.error === false && response.data) {
            return (response.data as any).categories || [];
          }
          return [];
        }),
        catchError(error => {
          console.error('Error getting categories:', error);
          return of([]);
        })
      );
  }

  /**
   * Reset state
   */
  resetState(): void {
    this.productsSubject.next([]);
    this.currentPage = 1;
    this.totalPages = 1;
    this.currentSearchParams = {};
    this.hasMoreSubject.next(true);
    this.loadingSubject.next(false);
  }

  /**
   * Lấy danh sách sản phẩm hiện tại
   */
  getCurrentProducts(): Product[] {
    return this.productsSubject.value;
  }

  /**
   * Kiểm tra có thể load more không
   */
  canLoadMore(): boolean {
    return this.currentPage < this.totalPages;
  }
}
